// Typing Test Application - Wrapped in IIFE for security
(function() {
    'use strict';

    // Security constants
    const MAX_REASONABLE_WPM = 300;
    const MIN_TEST_DURATION = 5; // seconds
    const MAX_ACCURACY = 100;

    // Validation helper functions
    function validateTestResults(wpm, accuracy, duration, textLength) {
        // Check for impossible WPM
        if (wpm > MAX_REASONABLE_WPM) {
            console.warn('Suspicious WPM detected:', wpm);
            return false;
        }

        // Check for impossible accuracy
        if (accuracy > MAX_ACCURACY || accuracy < 0) {
            console.warn('Invalid accuracy detected:', accuracy);
            return false;
        }

        // Check for unreasonably short test duration
        if (duration < MIN_TEST_DURATION) {
            console.warn('Test duration too short:', duration);
            return false;
        }

        // Check if text length makes sense for the time
        const minExpectedChars = Math.floor((wpm * 5 * duration) / 60 * 0.5); // 50% of expected
        if (textLength < minExpectedChars && accuracy > 90) {
            console.warn('Suspicious text length vs time ratio');
            return false;
        }

        return true;
    }

class TypingTest {
    constructor() {
        this.passages = null;
        this.currentPassage = null;
        this.settings = {
            duration: 60,
            difficulty: 'intermediate'
        };
        
        // Test state
        this.input = '';
        this.isActive = false;
        this.isCompleted = false;
        this.timeLeft = this.settings.duration;
        this.startTime = null;
        this.characterStatuses = [];
        this.timer = null;

        // Error tracking for tricky keys
        this.errorLog = [];
        this.trickyKeysData = {};
        this.isPracticeMode = false;
        this.practiceKey = null;

        // Charts
        this.wpmChart = null;
        this.accuracyChart = null;

        // DOM elements
        this.elements = {};

        this.init();
    }
    
    async init() {
        console.log('Initializing Typing Test...');
        this.bindElements();
        this.bindEvents();
        await this.loadPassages();
        this.trickyKeysData = this.loadTrickyKeysData();
        this.initializeTheme();
        this.updateDisplay();
        console.log('Typing Test initialized successfully');
    }
    
    bindElements() {
        this.elements = {
            // Settings
            durationSelect: document.getElementById('duration-select'),
            difficultySelect: document.getElementById('difficulty-select'),
            passageSelect: document.getElementById('passage-select'),
            
            // Stats
            timeLeft: document.getElementById('time-left'),
            wpm: document.getElementById('wpm'),
            accuracy: document.getElementById('accuracy'),
            characters: document.getElementById('characters'),
            
            // Text display
            passageTitle: document.getElementById('passage-title'),
            textDisplay: document.getElementById('text-display'),
            typingInput: document.getElementById('typing-input'),
            
            // Controls
            resetBtn: document.getElementById('reset-btn'),
            exitPracticeBtn: document.getElementById('exit-practice-btn'),

            // Tricky Keys
            trickyKeysSection: document.getElementById('tricky-keys-section'),
            trickyKeysList: document.getElementById('tricky-keys-list'),
            clearTrickyKeysBtn: document.getElementById('clear-tricky-keys-btn'),

            // Modal
            resultsModal: document.getElementById('results-modal'),
            closeModal: document.getElementById('close-modal'),
            tryAgainBtn: document.getElementById('try-again-btn'),

            // Final results
            finalWpm: document.getElementById('final-wpm'),
            finalAccuracy: document.getElementById('final-accuracy'),
            finalCharacters: document.getElementById('final-characters'),
            finalCorrect: document.getElementById('final-correct'),
            finalIncorrect: document.getElementById('final-incorrect'),
            finalTime: document.getElementById('final-time'),

            // Theme
            themeBtns: document.querySelectorAll('.theme-btn')
        };
    }
    
    bindEvents() {
        // Settings
        this.elements.durationSelect.addEventListener('change', (e) => {
            this.handleDurationChange(parseInt(e.target.value));
        });
        
        this.elements.difficultySelect.addEventListener('change', (e) => {
            this.handleDifficultyChange(e.target.value);
        });
        
        this.elements.passageSelect.addEventListener('change', (e) => {
            this.handlePassageChange(parseInt(e.target.value));
        });
        
        // Typing input
        this.elements.typingInput.addEventListener('input', (e) => {
            this.handleInputChange(e.target.value);
        });

        // Prevent non-English characters and paste
        this.elements.typingInput.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        this.elements.typingInput.addEventListener('paste', (e) => {
            e.preventDefault();
            this.showInputFeedback('Paste is not allowed during the test', 'error');
        });
        
        // Controls
        this.elements.resetBtn.addEventListener('click', () => {
            this.reset();
        });

        this.elements.exitPracticeBtn.addEventListener('click', () => {
            this.exitPracticeMode();
        });

        // Tricky Keys
        this.elements.clearTrickyKeysBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to clear all tricky keys data? This action cannot be undone.')) {
                this.clearTrickyKeysData();
                this.updateTrickyKeysDisplay();
            }
        });

        // Modal
        this.elements.closeModal.addEventListener('click', () => {
            this.hideResults();
        });
        
        this.elements.tryAgainBtn.addEventListener('click', () => {
            this.hideResults();
            this.reset();
        });
        
        // Theme
        this.elements.themeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.setTheme(btn.dataset.theme);
            });
        });
        
        // Modal overlay click
        this.elements.resultsModal.addEventListener('click', (e) => {
            if (e.target === this.elements.resultsModal) {
                this.hideResults();
            }
        });

        // Development shortcut for testing (Ctrl+Shift+T)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.simulateTestErrors();
                console.log('Test errors simulated via keyboard shortcut');
            }
        });
    }
    
    async loadPassages() {
        try {
            const response = await fetch('data/passages.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.passages = await response.json();
            this.updatePassageOptions();
            this.setInitialPassage();
        } catch (error) {
            console.error('Error loading passages:', error);
            // Fallback data
            this.passages = {
                beginner: [{
                    id: 1,
                    title: "Simple Introduction",
                    text: "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet."
                }],
                intermediate: [{
                    id: 2,
                    title: "Technology Evolution",
                    text: "The advancement of technology has revolutionized how we communicate and work. Modern keyboards respond instantly to our touch."
                }],
                advanced: [{
                    id: 3,
                    title: "Complex Syntax",
                    text: "Programming languages utilize intricate syntax patterns including brackets [], parentheses (), and various operators like +=, -=, *=."
                }]
            };
            this.updatePassageOptions();
            this.setInitialPassage();
        }
    }
    
    updatePassageOptions() {
        const select = this.elements.passageSelect;
        select.innerHTML = '';
        
        const passages = this.passages[this.settings.difficulty];
        passages.forEach(passage => {
            const option = document.createElement('option');
            option.value = passage.id;
            option.textContent = passage.title;
            select.appendChild(option);
        });
    }
    
    setInitialPassage() {
        if (this.passages && this.passages[this.settings.difficulty]) {
            this.currentPassage = this.passages[this.settings.difficulty][0];
            this.initializeCharacterStatuses();
            this.updateDisplay();
        }
    }
    
    initializeCharacterStatuses() {
        if (this.currentPassage) {
            this.characterStatuses = new Array(this.currentPassage.text.length).fill('pending');
        }
    }
    
    handleDurationChange(duration) {
        this.settings.duration = duration;
        this.reset();
    }
    
    handleDifficultyChange(difficulty) {
        this.settings.difficulty = difficulty;
        this.updatePassageOptions();
        if (this.passages) {
            this.currentPassage = this.passages[difficulty][0];
            this.reset();
        }
    }
    
    handlePassageChange(passageId) {
        if (this.passages) {
            const passage = this.passages[this.settings.difficulty].find(p => p.id === passageId);
            if (passage) {
                this.currentPassage = passage;
                this.reset();
            }
        }
    }
    
    handleInputChange(value) {
        if (this.isCompleted) return;

        // Start the test on first input
        if (!this.isActive && !this.startTime) {
            this.startTest();
        }

        this.input = value;
        this.updateCharacterStatuses();
        this.updateStats();

        // Check if completed (passage finished or time up)
        if (value.length === this.currentPassage.text.length) {
            this.completeTest();
        }

        this.updateTextDisplay();
    }

    handleKeyDown(e) {
        // Allow control keys and navigation
        if (e.ctrlKey || e.altKey || e.metaKey ||
            ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
             'Home', 'End', 'Tab', 'Escape', 'Enter'].includes(e.key)) {
            return;
        }

        // Define allowed characters for English typing test
        const allowedPattern = /^[a-zA-Z0-9\s.,;:!?'"()\-_@#$%&*+=<>\/\\[\]{}|`~]$/;

        if (e.key.length === 1 && !allowedPattern.test(e.key)) {
            e.preventDefault();
            this.showInputFeedback('Only English characters are allowed', 'warning');
        }
    }

    showInputFeedback(message, type = 'info') {
        const input = this.elements.typingInput;
        const originalBorder = input.style.border;

        // Apply visual feedback
        if (type === 'error') {
            input.style.border = '2px solid #ef4444';
        } else if (type === 'warning') {
            input.style.border = '2px solid #f59e0b';
        }

        // Add shake animation
        input.style.animation = 'shake 0.3s ease-in-out';

        // Reset after animation
        setTimeout(() => {
            input.style.border = originalBorder;
            input.style.animation = '';
        }, 300);

        // Optional: Show tooltip or console message
        console.log(`Input feedback: ${message}`);
    }

    startTest() {
        this.isActive = true;
        this.startTime = Date.now();
        this.elements.typingInput.placeholder = "Keep typing...";
        this.startTimer();
    }
    
    startTimer() {
        this.timer = setInterval(() => {
            this.timeLeft--;
            this.updateDisplay();
            
            if (this.timeLeft <= 0) {
                this.completeTest();
            }
        }, 1000);
    }
    
    completeTest() {
        this.isCompleted = true;
        this.isActive = false;
        this.elements.typingInput.disabled = true;
        
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        
        this.showResults();
    }
    
    updateCharacterStatuses() {
        const text = this.currentPassage.text;
        const inputLength = this.input.length;

        // Only update the character that was just typed
        if (inputLength > 0) {
            const lastIndex = inputLength - 1;
            const expectedChar = text[lastIndex];
            const typedChar = this.input[lastIndex];
            const isCorrect = typedChar === expectedChar;

            this.characterStatuses[lastIndex] = isCorrect ? 'correct' : 'incorrect';

            // Log error for tricky keys analysis
            if (!isCorrect) {
                this.logError(expectedChar, typedChar, lastIndex);
            }
        }
    }

    // Error tracking methods for tricky keys feature
    logError(expectedChar, typedChar, position) {
        const errorEntry = {
            expected: expectedChar,
            typed: typedChar,
            position: position,
            timestamp: Date.now(),
            passage: this.currentPassage ? this.currentPassage.title : 'Unknown',
            difficulty: this.settings.difficulty
        };

        this.errorLog.push(errorEntry);

        // Update persistent tricky keys data
        this.updateTrickyKeysData(expectedChar, typedChar);
    }

    updateTrickyKeysData(expectedChar, typedChar) {
        // Initialize character data if it doesn't exist
        if (!this.trickyKeysData[expectedChar]) {
            this.trickyKeysData[expectedChar] = {
                totalErrors: 0,
                mistypedAs: {},
                lastError: null
            };
        }

        // Update error count for this character
        this.trickyKeysData[expectedChar].totalErrors++;
        this.trickyKeysData[expectedChar].lastError = Date.now();

        // Track what this character was mistyped as
        if (!this.trickyKeysData[expectedChar].mistypedAs[typedChar]) {
            this.trickyKeysData[expectedChar].mistypedAs[typedChar] = 0;
        }
        this.trickyKeysData[expectedChar].mistypedAs[typedChar]++;

        // Save to localStorage
        this.saveTrickyKeysData();
    }

    loadTrickyKeysData() {
        try {
            const data = localStorage.getItem('typing-test-tricky-keys');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error loading tricky keys data:', error);
            return {};
        }
    }

    saveTrickyKeysData() {
        try {
            localStorage.setItem('typing-test-tricky-keys', JSON.stringify(this.trickyKeysData));
        } catch (error) {
            console.error('Error saving tricky keys data:', error);
        }
    }

    getTrickyKeys(limit = 5) {
        // Convert tricky keys data to array and sort by error count
        const trickyKeys = Object.entries(this.trickyKeysData)
            .map(([char, data]) => ({
                character: char,
                errorCount: data.totalErrors,
                mostCommonMistake: this.getMostCommonMistake(data.mistypedAs),
                lastError: data.lastError
            }))
            .filter(key => key.errorCount > 0)
            .sort((a, b) => b.errorCount - a.errorCount)
            .slice(0, limit);

        return trickyKeys;
    }

    getMostCommonMistake(mistypedAs) {
        let maxCount = 0;
        let mostCommon = '';

        for (const [char, count] of Object.entries(mistypedAs)) {
            if (count > maxCount) {
                maxCount = count;
                mostCommon = char;
            }
        }

        return mostCommon;
    }

    clearTrickyKeysData() {
        this.trickyKeysData = {};
        this.saveTrickyKeysData();
    }



    getTrickyKeysStats() {
        const totalErrors = Object.values(this.trickyKeysData)
            .reduce((sum, data) => sum + data.totalErrors, 0);

        const totalCharacters = Object.keys(this.trickyKeysData).length;

        const mostProblematicKey = this.getTrickyKeys(1)[0];

        return {
            totalErrors,
            totalCharacters,
            mostProblematicKey: mostProblematicKey ? {
                character: mostProblematicKey.character,
                errorCount: mostProblematicKey.errorCount
            } : null,
            dataSize: JSON.stringify(this.trickyKeysData).length
        };
    }

    // Test method to simulate errors for development/testing
    simulateTestErrors() {
        // Simulate some common typing errors for testing
        const testErrors = [
            { expected: 'e', typed: 'r', position: 5 },
            { expected: 'a', typed: 's', position: 12 },
            { expected: 'e', typed: 'w', position: 18 },
            { expected: 't', typed: 'y', position: 25 },
            { expected: 'a', typed: 's', position: 30 },
            { expected: 'e', typed: 'r', position: 35 },
            { expected: 'i', typed: 'o', position: 42 },
            { expected: 'o', typed: 'p', position: 48 }
        ];

        testErrors.forEach(error => {
            this.logError(error.expected, error.typed, error.position);
        });

        this.updateTrickyKeysDisplay();
        console.log('Test errors simulated. Tricky keys data:', this.trickyKeysData);
    }

    // Practice text generation for tricky keys
    generatePracticeText(targetChar, length = 200) {
        const practiceWords = this.getPracticeWords(targetChar);
        const sentences = this.generatePracticeSentences(targetChar, practiceWords);

        let practiceText = '';
        let currentLength = 0;
        let sentenceIndex = 0;

        while (currentLength < length && sentenceIndex < sentences.length) {
            const sentence = sentences[sentenceIndex];
            if (currentLength + sentence.length <= length) {
                practiceText += (practiceText ? ' ' : '') + sentence;
                currentLength += sentence.length + (practiceText ? 1 : 0);
            } else {
                break;
            }
            sentenceIndex++;
        }

        // If we need more text, cycle through sentences again
        while (currentLength < length) {
            const sentence = sentences[sentenceIndex % sentences.length];
            const remainingLength = length - currentLength;

            if (sentence.length <= remainingLength) {
                practiceText += ' ' + sentence;
                currentLength += sentence.length + 1;
            } else {
                // Add partial sentence if needed
                practiceText += ' ' + sentence.substring(0, remainingLength - 1);
                break;
            }
            sentenceIndex++;
        }

        return practiceText.trim();
    }

    getPracticeWords(targetChar) {
        const wordLists = {
            'a': ['amazing', 'always', 'about', 'after', 'again', 'against', 'all', 'almost', 'alone', 'along', 'already', 'also', 'although', 'among', 'and', 'another', 'any', 'anyone', 'anything', 'anywhere', 'appear', 'area', 'around', 'as', 'ask', 'at', 'attack', 'attempt', 'available', 'away'],
            'e': ['every', 'everyone', 'everything', 'everywhere', 'example', 'except', 'experience', 'explain', 'eye', 'even', 'ever', 'each', 'early', 'easy', 'eat', 'education', 'effect', 'eight', 'either', 'else', 'end', 'enough', 'entire', 'especially', 'evening', 'event', 'evidence', 'exactly', 'examine', 'excellent', 'exercise', 'expect', 'expensive', 'extreme'],
            'i': ['important', 'including', 'increase', 'indeed', 'indicate', 'individual', 'industry', 'information', 'inside', 'instead', 'interest', 'international', 'into', 'investment', 'involve', 'issue', 'item', 'its', 'itself', 'identify', 'if', 'imagine', 'immediately', 'impact', 'implement', 'imply', 'improve', 'in', 'include', 'income', 'incredible', 'independent'],
            'o': ['of', 'on', 'one', 'only', 'or', 'other', 'our', 'out', 'over', 'own', 'office', 'often', 'old', 'once', 'open', 'option', 'order', 'organization', 'original', 'others', 'outside', 'obvious', 'occur', 'offer', 'official', 'operation', 'opportunity', 'oppose', 'outcome', 'output', 'overall', 'overcome'],
            'u': ['under', 'understand', 'until', 'up', 'upon', 'use', 'used', 'useful', 'user', 'using', 'usual', 'usually', 'ultimate', 'unable', 'uncle', 'uncomfortable', 'unconscious', 'uncover', 'undergo', 'underlying', 'undertake', 'unexpected', 'unfortunately', 'uniform', 'union', 'unique', 'unit', 'unite', 'unity', 'universal', 'university'],
            't': ['the', 'that', 'this', 'they', 'them', 'their', 'there', 'these', 'those', 'through', 'time', 'to', 'today', 'together', 'too', 'top', 'total', 'toward', 'town', 'trade', 'traditional', 'training', 'travel', 'treat', 'treatment', 'tree', 'trial', 'trip', 'trouble', 'truck', 'true', 'trust', 'truth', 'try', 'turn'],
            's': ['some', 'so', 'such', 'system', 'see', 'seem', 'several', 'she', 'should', 'show', 'since', 'small', 'social', 'society', 'something', 'sometimes', 'soon', 'sort', 'sound', 'source', 'south', 'space', 'speak', 'special', 'specific', 'spend', 'staff', 'stage', 'standard', 'start', 'state', 'station', 'stay', 'step', 'still', 'stock', 'stop', 'store', 'story', 'strategy', 'street', 'strong', 'structure', 'student', 'study', 'stuff', 'style', 'subject', 'success', 'successful', 'suddenly', 'suffer', 'suggest', 'summer', 'support', 'sure', 'surface', 'surprise', 'surround', 'survey', 'survive', 'suspect', 'sweet', 'switch', 'symbol', 'system'],
            'r': ['really', 'right', 'room', 'run', 'rather', 'reach', 'read', 'ready', 'real', 'reason', 'receive', 'recent', 'recognize', 'record', 'red', 'reduce', 'refer', 'reflect', 'region', 'relate', 'relationship', 'remain', 'remember', 'remove', 'report', 'represent', 'require', 'research', 'resource', 'respond', 'response', 'responsibility', 'rest', 'result', 'return', 'reveal', 'review', 'rich', 'ride', 'ring', 'rise', 'risk', 'river', 'road', 'rock', 'role', 'roll', 'roof', 'root', 'round', 'route', 'row', 'rule', 'rural'],
            'n': ['not', 'now', 'new', 'no', 'need', 'never', 'next', 'night', 'nothing', 'number', 'name', 'nation', 'national', 'natural', 'nature', 'near', 'nearly', 'necessary', 'neck', 'network', 'news', 'newspaper', 'nice', 'nine', 'nineteen', 'ninety', 'nobody', 'nod', 'noise', 'none', 'noon', 'nor', 'normal', 'normally', 'north', 'northern', 'nose', 'note', 'notice', 'novel', 'nuclear', 'nurse'],
            'l': ['like', 'look', 'long', 'last', 'large', 'late', 'later', 'law', 'lay', 'lead', 'leader', 'learn', 'least', 'leave', 'left', 'legal', 'less', 'let', 'letter', 'level', 'lie', 'life', 'light', 'line', 'list', 'listen', 'little', 'live', 'local', 'lose', 'loss', 'lot', 'love', 'low', 'lunch', 'lady', 'lake', 'land', 'language', 'laptop', 'largely', 'laser', 'laugh', 'launch', 'lawyer', 'layer', 'lazy', 'league', 'lean', 'leather', 'legal', 'legend', 'length', 'lens', 'lesson', 'level', 'liberal', 'library', 'license', 'lid', 'lift', 'likely', 'limit', 'link', 'lion', 'liquid', 'literally', 'literature', 'loan', 'lobby', 'location', 'lock', 'logic', 'lonely', 'loose', 'lord', 'loud', 'lovely', 'lower', 'lucky', 'lunch', 'luxury']
        };

        // Return words for the target character, or generate generic words if not found
        return wordLists[targetChar.toLowerCase()] || this.generateGenericWords(targetChar);
    }

    generateGenericWords(targetChar) {
        // Fallback: generate simple words containing the target character
        const commonWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'man', 'end', 'few', 'got', 'let', 'put', 'say', 'she', 'too', 'use'];
        return commonWords.filter(word => word.includes(targetChar.toLowerCase()));
    }

    generatePracticeSentences(targetChar, words) {
        const sentences = [];
        const char = targetChar.toLowerCase();

        // Create sentences with high frequency of the target character
        if (words.length > 0) {
            // Generate 10-15 sentences using the practice words
            for (let i = 0; i < 15; i++) {
                const sentence = this.createSentenceWithWords(words, char);
                if (sentence) {
                    sentences.push(sentence);
                }
            }
        }

        // Add some generic sentences if we don't have enough
        if (sentences.length < 5) {
            sentences.push(...this.getGenericSentences(char));
        }

        return sentences;
    }

    createSentenceWithWords(words, targetChar) {
        const templates = [
            'The {word1} {word2} {word3} quickly.',
            '{word1} and {word2} are {word3}.',
            'Every {word1} needs {word2} and {word3}.',
            'When {word1} meets {word2}, {word3} happens.',
            'The {word1} {word2} creates {word3} results.',
            'Both {word1} and {word2} require {word3}.',
            'After {word1}, the {word2} becomes {word3}.',
            'During {word1}, we see {word2} and {word3}.',
            'The {word1} process involves {word2} and {word3}.',
            'Each {word1} contains {word2} and {word3} elements.'
        ];

        const template = templates[Math.floor(Math.random() * templates.length)];
        const selectedWords = this.getRandomWords(words, 3);

        if (selectedWords.length < 3) return null;

        return template
            .replace('{word1}', selectedWords[0])
            .replace('{word2}', selectedWords[1])
            .replace('{word3}', selectedWords[2]);
    }

    getRandomWords(words, count) {
        const shuffled = [...words].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, count);
    }

    getGenericSentences(targetChar) {
        const genericSentences = {
            'e': [
                'The green trees swayed in the gentle breeze.',
                'She believed she could achieve excellence.',
                'We need to keep these essential elements separate.',
                'The teacher explained the lesson with great detail.',
                'Every student received excellent feedback.',
                'The weather seemed perfect for the event.'
            ],
            'a': [
                'Amazing animals always appear after rain.',
                'All amazing adventures await ahead.',
                'The cat sat on the mat and ate.',
                'Many animals gather around the water.',
                'The amazing artwork attracted attention.',
                'Always ask about available alternatives.'
            ],
            'i': [
                'This is an important initial initiative.',
                'The individual insisted on immediate implementation.',
                'Writing requires imagination and inspiration.',
                'The interview included interesting insights.',
                'Digital information improves decision making.',
                'The initial investigation indicated issues.'
            ],
            'o': [
                'The old oak stood strong for long.',
                'Our organization offers outstanding opportunities.',
                'The doctor provided proper protocol.',
                'Most people love good food and company.',
                'The robot worked on the top floor.',
                'Tomorrow we will explore more options.'
            ],
            'u': [
                'The university students studied unusual subjects.',
                'Under the umbrella, the duck stayed dry.',
                'The music sounded beautiful and unique.',
                'The future requires understanding and unity.',
                'The puzzle proved difficult but fun.',
                'The truck rumbled up the bumpy road.'
            ]
        };

        return genericSentences[targetChar] || [
            `Practice typing the letter ${targetChar} repeatedly.`,
            `Focus on accuracy when typing ${targetChar}.`,
            `The ${targetChar} key requires careful attention.`
        ];
    }

    // Practice mode functionality
    startPracticeMode(targetChar) {
        this.isPracticeMode = true;
        this.practiceKey = targetChar;

        // Generate practice text
        const practiceText = this.generatePracticeText(targetChar, 300);

        // Create a temporary passage for practice
        this.currentPassage = {
            id: 'practice',
            title: `Practice: "${targetChar}" key`,
            text: practiceText
        };

        // Reset and start the practice session
        this.reset();
        this.updateDisplay();
    }

    exitPracticeMode() {
        this.isPracticeMode = false;
        this.practiceKey = null;

        // Return to the original passage
        this.setInitialPassage();
        this.reset();

        // Update UI
        this.elements.exitPracticeBtn.classList.add('hidden');
        this.updateTrickyKeysDisplay();
    }

    // Tricky Keys Display Methods
    updateTrickyKeysDisplay() {
        const trickyKeys = this.getTrickyKeys(5);
        const listContainer = this.elements.trickyKeysList;

        if (trickyKeys.length === 0) {
            this.showNoTrickyKeysMessage();
        } else {
            this.renderTrickyKeysList(trickyKeys);
        }
    }

    showNoTrickyKeysMessage() {
        this.elements.trickyKeysList.innerHTML = `
            <div class="no-data-message">
                <div class="no-data-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                        <path d="M12 17h.01"/>
                    </svg>
                </div>
                <h4>No tricky keys yet</h4>
                <p>Start typing to identify your most challenging keys. The system will track your errors and help you practice specific characters you struggle with.</p>
            </div>
        `;
    }

    renderTrickyKeysList(trickyKeys) {
        const listHTML = trickyKeys.map(key => this.createTrickyKeyItem(key)).join('');
        this.elements.trickyKeysList.innerHTML = `
            <div class="tricky-keys-grid">
                ${listHTML}
            </div>
        `;

        // Add event listeners to practice buttons
        this.bindPracticeButtons();
    }

    createTrickyKeyItem(keyData) {
        const { character, errorCount, mostCommonMistake } = keyData;
        const displayChar = character === ' ' ? 'Space' : character;
        const mistakeDisplay = mostCommonMistake === ' ' ? 'Space' : mostCommonMistake;

        return `
            <div class="tricky-key-item">
                <div class="key-info">
                    <div class="key-character">${displayChar}</div>
                    <div class="key-stats">
                        <div class="error-count">${errorCount} error${errorCount !== 1 ? 's' : ''}</div>
                        ${mostCommonMistake ? `<div class="common-mistake">Often typed as: <span class="mistake-char">${mistakeDisplay}</span></div>` : ''}
                    </div>
                </div>
                <button type="button" class="practice-btn" data-char="${character}" title="Practice typing '${displayChar}'">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="5,3 19,12 5,21"/>
                    </svg>
                    Practice
                </button>
            </div>
        `;
    }

    bindPracticeButtons() {
        const practiceButtons = this.elements.trickyKeysList.querySelectorAll('.practice-btn');
        practiceButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetChar = e.currentTarget.dataset.char;
                this.startPracticeMode(targetChar);
            });
        });
    }

    calculateStats() {
        const totalCharacters = this.input.length;
        const correctCharacters = this.characterStatuses.slice(0, totalCharacters)
            .filter(status => status === 'correct').length;
        const incorrectCharacters = totalCharacters - correctCharacters;

        const accuracy = totalCharacters > 0 ? Math.round((correctCharacters / totalCharacters) * 100) : 100;

        const timeElapsed = this.isActive || this.isCompleted
            ? (this.settings.duration - this.timeLeft)
            : 0;

        const minutes = timeElapsed / 60;
        const words = correctCharacters / 5; // Standard: 5 characters = 1 word
        const wpm = minutes > 0 ? Math.round(words / minutes) : 0;

        return {
            wpm,
            accuracy,
            totalCharacters,
            correctCharacters,
            incorrectCharacters,
            timeElapsed
        };
    }
    
    updateStats() {
        const stats = this.calculateStats();
        
        this.elements.wpm.textContent = stats.wpm;
        this.elements.accuracy.textContent = stats.accuracy;
        this.elements.characters.textContent = stats.totalCharacters;
    }
    
    updateDisplay() {
        this.elements.timeLeft.textContent = this.timeLeft;

        if (this.currentPassage) {
            this.elements.passageTitle.textContent = this.currentPassage.title;
            this.updateTextDisplay();
        }

        this.updateStats();
        this.updateTrickyKeysDisplay();

        // Update practice mode UI
        if (this.isPracticeMode) {
            this.elements.exitPracticeBtn.classList.remove('hidden');
        } else {
            this.elements.exitPracticeBtn.classList.add('hidden');
        }
    }
    
    updateTextDisplay() {
        if (!this.currentPassage) return;

        const text = this.currentPassage.text;
        const display = this.elements.textDisplay;

        // Only create DOM elements once when passage changes
        if (!this.textSpans || this.textSpans.length !== text.length) {
            display.innerHTML = '';
            this.textSpans = [];

            text.split('').forEach((char, index) => {
                const span = document.createElement('span');
                span.textContent = char;
                span.className = 'typing-char untyped';
                this.textSpans.push(span);
                display.appendChild(span);
            });
        }

        // Only update classes for characters that need updating
        const inputLength = this.input.length;

        // Update only the range that might have changed
        const startIndex = Math.max(0, inputLength - 1);
        const endIndex = Math.min(text.length, inputLength + 2);

        for (let i = startIndex; i < endIndex; i++) {
            const span = this.textSpans[i];
            if (!span) continue;

            if (i < inputLength) {
                span.className = 'typing-char ' + this.characterStatuses[i];
            } else if (i === inputLength && inputLength < text.length) {
                span.className = 'typing-char current';
            } else {
                span.className = 'typing-char untyped';
            }
        }
    }
    
    showResults() {
        const stats = this.calculateStats();

        // Validate test results for potential manipulation
        const isValid = validateTestResults(
            stats.wpm,
            stats.accuracy,
            stats.timeElapsed,
            stats.totalCharacters
        );

        if (!isValid) {
            // Show warning but still display results
            console.warn('Test results appear suspicious and may have been manipulated');
            this.elements.finalWpm.textContent = `${stats.wpm} (?)`;
        } else {
            this.elements.finalWpm.textContent = stats.wpm;
        }

        this.elements.finalAccuracy.textContent = `${stats.accuracy}%`;
        this.elements.finalCharacters.textContent = stats.totalCharacters;
        this.elements.finalCorrect.textContent = stats.correctCharacters;
        this.elements.finalIncorrect.textContent = stats.incorrectCharacters;
        this.elements.finalTime.textContent = `${stats.timeElapsed}s`;

        this.elements.resultsModal.classList.remove('hidden');
    }
    
    hideResults() {
        this.elements.resultsModal.classList.add('hidden');
    }
    
    reset() {
        this.input = '';
        this.isActive = false;
        this.isCompleted = false;
        this.timeLeft = this.settings.duration;
        this.startTime = null;

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        this.elements.typingInput.value = '';
        this.elements.typingInput.disabled = false;
        this.elements.typingInput.placeholder = "Click here and start typing to begin the test";

        // Reset text spans to force recreation
        this.textSpans = null;

        this.initializeCharacterStatuses();
        this.updateDisplay();
        this.hideResults();

        // Focus the input
        setTimeout(() => {
            this.elements.typingInput.focus();
        }, 100);
    }
    
    // Theme Management
    initializeTheme() {
        const savedTheme = localStorage.getItem('typing-test-theme') || 'system';
        this.setTheme(savedTheme);
    }
    
    setTheme(theme) {
        localStorage.setItem('typing-test-theme', theme);
        
        // Update active button
        this.elements.themeBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.theme === theme);
        });
        
        // Apply theme
        const root = document.documentElement;
        root.classList.remove('light', 'dark');
        
        if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            root.classList.add(systemTheme);
        } else {
            root.classList.add(theme);
        }
        
        // Set color-scheme for browser UI
        root.style.colorScheme = theme === 'system' 
            ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
            : theme;
    }
}

// Typing Benchmark Class
class TypingBenchmark {
    constructor() {
        this.benchmarkTexts = null;
        this.currentBenchmarkText = null;
        this.settings = {
            duration: 90 // Fixed 90 seconds for benchmark
        };

        // Test state
        this.input = '';
        this.isActive = false;
        this.isCompleted = false;
        this.timeLeft = this.settings.duration;
        this.startTime = null;
        this.characterStatuses = [];
        this.timer = null;
        this.currentTextIndex = 0;

        // Charts
        this.wpmChart = null;
        this.accuracyChart = null;

        // DOM elements
        this.elements = {};

        this.init();
    }

    async init() {
        console.log('Initializing Typing Benchmark...');
        this.bindElements();
        this.bindEvents();
        await this.loadBenchmarkTexts();
        this.updateDisplay();
        console.log('Typing Benchmark initialized successfully');
    }

    bindElements() {
        // Navigation elements
        this.elements.practiceTab = document.getElementById('practice-tab');
        this.elements.benchmarkTab = document.getElementById('benchmark-tab');
        this.elements.practiceSection = document.getElementById('practice-section');
        this.elements.benchmarkSection = document.getElementById('benchmark-section');

        // Benchmark test elements
        this.elements.startBenchmarkBtn = document.getElementById('start-benchmark-btn');
        this.elements.benchmarkTestInterface = document.getElementById('benchmark-test-interface');
        this.elements.benchmarkTypingInput = document.getElementById('benchmark-typing-input');
        this.elements.benchmarkTextDisplay = document.getElementById('benchmark-text-display');
        this.elements.benchmarkPassageTitle = document.getElementById('benchmark-passage-title');

        // Benchmark stats elements
        this.elements.benchmarkTimeLeft = document.getElementById('benchmark-time-left');
        this.elements.benchmarkWpm = document.getElementById('benchmark-wpm');
        this.elements.benchmarkAccuracy = document.getElementById('benchmark-accuracy');

        // Benchmark results elements
        this.elements.benchmarkResults = document.getElementById('benchmark-results');
        this.elements.benchmarkFinalWpm = document.getElementById('benchmark-final-wpm');
        this.elements.benchmarkFinalAccuracy = document.getElementById('benchmark-final-accuracy');
        this.elements.skillLevelBadge = document.getElementById('skill-level-badge');
        this.elements.skillLevelTitle = document.getElementById('skill-level-title');
        this.elements.skillLevelDescription = document.getElementById('skill-level-description');

        // Action buttons
        this.elements.benchmarkTryAgainBtn = document.getElementById('benchmark-try-again-btn');
        this.elements.viewHistoryBtn = document.getElementById('view-history-btn');
        this.elements.clearHistoryBtn = document.getElementById('clear-history-btn');
        this.elements.exportHistoryBtn = document.getElementById('export-history-btn');

        // Charts and history
        this.elements.benchmarkCharts = document.getElementById('benchmark-charts');
        this.elements.benchmarkHistory = document.getElementById('benchmark-history');
        this.elements.benchmarkHistoryTable = document.getElementById('benchmark-history-table');
    }

    bindEvents() {
        // Navigation events
        this.elements.practiceTab?.addEventListener('click', () => this.switchToSection('practice'));
        this.elements.benchmarkTab?.addEventListener('click', () => this.switchToSection('benchmark'));

        // Benchmark test events
        this.elements.startBenchmarkBtn?.addEventListener('click', () => this.startBenchmarkTest());
        this.elements.benchmarkTypingInput?.addEventListener('input', (e) => this.handleBenchmarkInput(e.target.value));
        this.elements.benchmarkTryAgainBtn?.addEventListener('click', () => this.resetBenchmark());

        // Benchmark security measures
        this.elements.benchmarkTypingInput?.addEventListener('keydown', (e) => {
            this.handleBenchmarkKeyDown(e);
        });

        this.elements.benchmarkTypingInput?.addEventListener('paste', (e) => {
            e.preventDefault();
            this.showBenchmarkInputFeedback('Paste is not allowed during the benchmark test', 'error');
        });

        // History events
        this.elements.viewHistoryBtn?.addEventListener('click', () => this.showHistory());
        this.elements.clearHistoryBtn?.addEventListener('click', () => this.clearHistory());
        this.elements.exportHistoryBtn?.addEventListener('click', () => this.exportHistory());
    }

    async loadBenchmarkTexts() {
        try {
            const response = await fetch('./data/passages.json');
            const data = await response.json();
            this.benchmarkTexts = data.benchmark || [];
            console.log('Benchmark texts loaded:', this.benchmarkTexts.length);
        } catch (error) {
            console.error('Error loading benchmark texts:', error);
            this.benchmarkTexts = [];
        }
    }

    switchToSection(section) {
        // Save the active section to localStorage
        localStorage.setItem('typing-test-active-section', section);

        // Update tab states
        document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
        document.querySelectorAll('.test-section').forEach(section => section.classList.remove('active'));

        if (section === 'practice') {
            this.elements.practiceTab?.classList.add('active');
            this.elements.practiceSection?.classList.add('active');
        } else if (section === 'benchmark') {
            this.elements.benchmarkTab?.classList.add('active');
            this.elements.benchmarkSection?.classList.add('active');
            this.loadBenchmarkHistory();
            this.updateCharts();
        }
    }

    startBenchmarkTest() {
        // Select next benchmark text in rotation
        if (this.benchmarkTexts.length === 0) {
            console.error('No benchmark texts available');
            return;
        }

        this.currentBenchmarkText = this.benchmarkTexts[this.currentTextIndex];
        this.currentTextIndex = (this.currentTextIndex + 1) % this.benchmarkTexts.length;

        // Reset test state
        this.input = '';
        this.isActive = false;
        this.isCompleted = false;
        this.timeLeft = this.settings.duration;
        this.startTime = null;
        this.characterStatuses = [];

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        // Initialize character statuses
        this.initializeBenchmarkCharacterStatuses();

        // Show test interface and hide start button
        this.elements.startBenchmarkBtn?.classList.add('hidden');
        this.elements.benchmarkTestInterface?.classList.remove('hidden');
        this.elements.benchmarkResults?.classList.add('hidden');

        // Setup text display
        this.elements.benchmarkPassageTitle.textContent = this.currentBenchmarkText.title;
        this.updateBenchmarkTextDisplay();

        // Clear and focus input
        this.elements.benchmarkTypingInput.value = '';
        this.elements.benchmarkTypingInput.disabled = false;
        this.elements.benchmarkTypingInput.placeholder = "Click here and start typing to begin the benchmark test";

        // Update display
        this.updateDisplay();

        // Focus the input
        setTimeout(() => {
            this.elements.benchmarkTypingInput?.focus();
        }, 100);
    }

    initializeBenchmarkCharacterStatuses() {
        this.characterStatuses = new Array(this.currentBenchmarkText.text.length).fill('untyped');
    }

    handleBenchmarkInput(value) {
        if (this.isCompleted) return;

        // Start the test on first input
        if (!this.isActive && !this.startTime) {
            this.startBenchmarkTimer();
        }

        this.input = value;
        this.updateBenchmarkCharacterStatuses();
        this.updateBenchmarkStats();

        // Check if completed (passage finished or time up)
        if (value.length === this.currentBenchmarkText.text.length) {
            this.completeBenchmarkTest();
        }

        this.updateBenchmarkTextDisplay();
    }

    handleBenchmarkKeyDown(e) {
        // Allow control keys and navigation
        if (e.ctrlKey || e.altKey || e.metaKey ||
            ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
             'Home', 'End', 'Tab', 'Escape', 'Enter'].includes(e.key)) {
            return;
        }

        // Define allowed characters for English typing test
        const allowedPattern = /^[a-zA-Z0-9\s.,;:!?'"()\-_@#$%&*+=<>\/\\[\]{}|`~]$/;

        if (e.key.length === 1 && !allowedPattern.test(e.key)) {
            e.preventDefault();
            this.showBenchmarkInputFeedback('Only English characters are allowed', 'warning');
        }
    }

    showBenchmarkInputFeedback(message, type = 'info') {
        const input = this.elements.benchmarkTypingInput;
        const originalBorder = input.style.border;

        // Apply visual feedback
        if (type === 'error') {
            input.style.border = '2px solid #ef4444';
        } else if (type === 'warning') {
            input.style.border = '2px solid #f59e0b';
        }

        // Add shake animation
        input.style.animation = 'shake 0.3s ease-in-out';

        // Reset after animation
        setTimeout(() => {
            input.style.border = originalBorder;
            input.style.animation = '';
        }, 300);

        // Optional: Show tooltip or console message
        console.log(`Benchmark input feedback: ${message}`);
    }

    startBenchmarkTimer() {
        this.isActive = true;
        this.startTime = Date.now();
        this.elements.benchmarkTypingInput.placeholder = "Keep typing...";

        this.timer = setInterval(() => {
            this.timeLeft--;
            this.updateDisplay();

            if (this.timeLeft <= 0) {
                this.completeBenchmarkTest();
            }
        }, 1000);
    }

    updateBenchmarkCharacterStatuses() {
        const text = this.currentBenchmarkText.text;
        const inputLength = this.input.length;

        // Only update the character that was just typed
        if (inputLength > 0) {
            const lastIndex = inputLength - 1;
            const expectedChar = text[lastIndex];
            const typedChar = this.input[lastIndex];
            const isCorrect = typedChar === expectedChar;

            this.characterStatuses[lastIndex] = isCorrect ? 'correct' : 'incorrect';
        }
    }

    updateBenchmarkStats() {
        const stats = this.calculateBenchmarkStats();

        this.elements.benchmarkWpm.textContent = stats.wpm;
        this.elements.benchmarkAccuracy.textContent = `${stats.accuracy}%`;
    }

    calculateBenchmarkStats() {
        const totalCharacters = this.input.length;
        const correctCharacters = this.characterStatuses.slice(0, totalCharacters)
            .filter(status => status === 'correct').length;
        const incorrectCharacters = totalCharacters - correctCharacters;

        const accuracy = totalCharacters > 0 ? Math.round((correctCharacters / totalCharacters) * 100) : 100;

        const timeElapsed = this.isActive || this.isCompleted
            ? (this.settings.duration - this.timeLeft)
            : 0;

        const minutes = timeElapsed / 60;
        const words = correctCharacters / 5; // Standard: 5 characters = 1 word
        const wpm = minutes > 0 ? Math.round(words / minutes) : 0;

        return {
            wpm,
            accuracy,
            totalCharacters,
            correctCharacters,
            incorrectCharacters,
            timeElapsed
        };
    }

    completeBenchmarkTest() {
        this.isCompleted = true;
        this.isActive = false;
        this.elements.benchmarkTypingInput.disabled = true;

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        this.showBenchmarkResults();
    }

    showBenchmarkResults() {
        const stats = this.calculateBenchmarkStats();

        // Validate benchmark results for potential manipulation
        const isValid = validateTestResults(
            stats.wpm,
            stats.accuracy,
            stats.timeElapsed,
            stats.totalCharacters
        );

        if (!isValid) {
            // Don't save suspicious results to history
            console.warn('Benchmark results appear suspicious and will not be saved');
            this.elements.benchmarkFinalWpm.textContent = `${stats.wpm} (Invalid)`;
        } else {
            // Save result to history only if valid
            this.saveBenchmarkResult(stats);
            this.elements.benchmarkFinalWpm.textContent = stats.wpm;
        }

        // Display results
        this.elements.benchmarkFinalAccuracy.textContent = `${stats.accuracy}%`;

        // Calculate and display skill level
        const skillLevel = this.calculateSkillLevel(stats.wpm, stats.accuracy);
        this.displaySkillLevel(skillLevel);

        // Hide test interface and show results
        this.elements.benchmarkTestInterface?.classList.add('hidden');
        this.elements.benchmarkResults?.classList.remove('hidden');

        // Update charts with new data
        this.updateCharts();
    }

    calculateSkillLevel(wpm, accuracy) {
        // Skill level classification based on WPM and accuracy
        if (wpm >= 91 && accuracy >= 96) {
            return {
                level: 'Expert',
                description: 'Outstanding typing skills with exceptional speed and accuracy',
                color: '#8b5cf6',
                icon: '🏆'
            };
        } else if (wpm >= 71 && accuracy >= 94) {
            return {
                level: 'Advanced',
                description: 'Excellent typing skills suitable for professional work',
                color: '#3b82f6',
                icon: '⭐'
            };
        } else if (wpm >= 51 && accuracy >= 92) {
            return {
                level: 'Proficient',
                description: 'Good typing skills for most daily tasks',
                color: '#22c55e',
                icon: '✅'
            };
        } else if (wpm >= 31 && accuracy >= 90) {
            return {
                level: 'Intermediate',
                description: 'Developing typing skills with room for improvement',
                color: '#f59e0b',
                icon: '📈'
            };
        } else {
            return {
                level: 'Novice',
                description: 'Beginning typing skills - keep practicing!',
                color: '#ef4444',
                icon: '🌱'
            };
        }
    }

    displaySkillLevel(skillLevel) {
        this.elements.skillLevelTitle.textContent = skillLevel.level;
        this.elements.skillLevelDescription.textContent = skillLevel.description;
        this.elements.skillLevelBadge.style.borderColor = skillLevel.color;
        this.elements.skillLevelBadge.querySelector('.badge-icon').textContent = skillLevel.icon;
    }

    updateBenchmarkTextDisplay() {
        if (!this.currentBenchmarkText) return;

        const text = this.currentBenchmarkText.text;
        const inputLength = this.input.length;

        // Clear the display and create spans using DOM methods for better wrapping
        this.elements.benchmarkTextDisplay.innerHTML = '';

        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            let className = 'typing-char ';

            if (i < inputLength) {
                className += this.characterStatuses[i];
            } else if (i === inputLength) {
                className += 'current';
            } else {
                className += 'untyped';
            }

            const span = document.createElement('span');
            span.className = className;
            span.textContent = char;

            this.elements.benchmarkTextDisplay.appendChild(span);
        }
    }

    updateDisplay() {
        this.elements.benchmarkTimeLeft.textContent = this.timeLeft;
    }

    resetBenchmark() {
        // Hide results and show start button
        this.elements.benchmarkResults?.classList.add('hidden');
        this.elements.benchmarkTestInterface?.classList.add('hidden');
        this.elements.startBenchmarkBtn?.classList.remove('hidden');

        // Reset state
        this.input = '';
        this.isActive = false;
        this.isCompleted = false;
        this.timeLeft = this.settings.duration;
        this.startTime = null;
        this.characterStatuses = [];

        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        this.updateDisplay();
    }

    // Benchmark history and data persistence methods
    saveBenchmarkResult(stats) {
        try {
            const result = {
                id: Date.now().toString(),
                date: new Date().toISOString(),
                wpm: stats.wpm,
                accuracy: stats.accuracy,
                totalCharacters: stats.totalCharacters,
                correctCharacters: stats.correctCharacters,
                incorrectCharacters: stats.incorrectCharacters,
                timeElapsed: stats.timeElapsed,
                textTitle: this.currentBenchmarkText.title,
                skillLevel: this.calculateSkillLevel(stats.wpm, stats.accuracy).level
            };

            const history = this.getBenchmarkHistory();
            history.push(result);

            // Keep only the last 100 results to prevent localStorage from getting too large
            if (history.length > 100) {
                history.splice(0, history.length - 100);
            }

            localStorage.setItem('typing-benchmark-history', JSON.stringify(history));
            console.log('Benchmark result saved:', result);

            // Update history display
            this.updateHistoryDisplay();

        } catch (error) {
            console.error('Error saving benchmark result:', error);
        }
    }

    getBenchmarkHistory() {
        try {
            const data = localStorage.getItem('typing-benchmark-history');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error loading benchmark history:', error);
            return [];
        }
    }

    loadBenchmarkHistory() {
        this.updateHistoryDisplay();
        this.updateBenchmarkStats();
    }

    updateHistoryDisplay() {
        const history = this.getBenchmarkHistory();
        const container = this.elements.benchmarkHistoryTable;

        if (history.length === 0) {
            container.innerHTML = `
                <div class="no-data-message">
                    <div class="no-data-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M9 11H7l5-8 5 8h-2l-1 7-4-7z"/>
                        </svg>
                    </div>
                    <h4>No benchmark history yet</h4>
                    <p>Take your first benchmark test to start tracking your progress and see your improvement over time.</p>
                </div>
            `;
            return;
        }

        // Sort history by date (newest first)
        const sortedHistory = history.sort((a, b) => new Date(b.date) - new Date(a.date));

        let tableHTML = `
            <table class="history-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>WPM</th>
                        <th>Accuracy</th>
                        <th>Skill Level</th>
                        <th>Text</th>
                    </tr>
                </thead>
                <tbody>
        `;

        sortedHistory.forEach(result => {
            const date = new Date(result.date);
            const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const skillLevel = result.skillLevel || this.calculateSkillLevel(result.wpm, result.accuracy).level;

            tableHTML += `
                <tr>
                    <td>${formattedDate}</td>
                    <td class="wpm-cell">${result.wpm}</td>
                    <td class="accuracy-cell">${result.accuracy}%</td>
                    <td class="skill-level-cell">
                        <span class="skill-badge skill-${skillLevel.toLowerCase()}">${skillLevel}</span>
                    </td>
                    <td class="text-cell">${result.textTitle}</td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;
    }

    updateBenchmarkStats() {
        const history = this.getBenchmarkHistory();

        if (history.length === 0) return;

        // Calculate statistics
        const totalTests = history.length;
        const avgWpm = Math.round(history.reduce((sum, result) => sum + result.wpm, 0) / totalTests);
        const avgAccuracy = Math.round(history.reduce((sum, result) => sum + result.accuracy, 0) / totalTests);
        const bestWpm = Math.max(...history.map(result => result.wpm));
        const bestAccuracy = Math.max(...history.map(result => result.accuracy));

        // Find most recent skill level
        const latestResult = history.sort((a, b) => new Date(b.date) - new Date(a.date))[0];
        const currentSkillLevel = latestResult ? latestResult.skillLevel : 'Novice';

        console.log('Benchmark stats:', {
            totalTests,
            avgWpm,
            avgAccuracy,
            bestWpm,
            bestAccuracy,
            currentSkillLevel
        });
    }

    updateCharts() {
        const history = this.getBenchmarkHistory();

        if (history.length === 0) {
            // Hide charts if no data
            this.elements.benchmarkCharts?.classList.add('hidden');
            return;
        }

        // Show charts
        this.elements.benchmarkCharts?.classList.remove('hidden');

        // Sort history by date (oldest first for charts)
        const sortedHistory = history.sort((a, b) => new Date(a.date) - new Date(b.date));

        // Prepare data for charts
        const labels = sortedHistory.map(result => {
            const date = new Date(result.date);
            return date.toLocaleDateString();
        });

        const wpmData = sortedHistory.map(result => result.wpm);
        const accuracyData = sortedHistory.map(result => result.accuracy);

        // Update WPM chart
        this.updateWpmChart(labels, wpmData);

        // Update Accuracy chart
        this.updateAccuracyChart(labels, accuracyData);
    }

    updateWpmChart(labels, data) {
        const ctx = document.getElementById('wpm-chart');
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.wpmChart) {
            this.wpmChart.destroy();
        }

        this.wpmChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Words Per Minute',
                    data: data,
                    borderColor: '#22c55e',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#22c55e',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#22c55e',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return `WPM: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Test Date',
                            color: '#64748b'
                        },
                        ticks: {
                            color: '#64748b',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: 'rgba(100, 116, 139, 0.1)'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Words Per Minute',
                            color: '#64748b'
                        },
                        ticks: {
                            color: '#64748b',
                            beginAtZero: true
                        },
                        grid: {
                            color: 'rgba(100, 116, 139, 0.1)'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    updateAccuracyChart(labels, data) {
        const ctx = document.getElementById('accuracy-chart');
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.accuracyChart) {
            this.accuracyChart.destroy();
        }

        this.accuracyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Accuracy Percentage',
                    data: data,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#3b82f6',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return `Accuracy: ${context.parsed.y}%`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Test Date',
                            color: '#64748b'
                        },
                        ticks: {
                            color: '#64748b',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: 'rgba(100, 116, 139, 0.1)'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Accuracy (%)',
                            color: '#64748b'
                        },
                        min: 0,
                        max: 100,
                        ticks: {
                            color: '#64748b',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(100, 116, 139, 0.1)'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    showHistory() {
        // Scroll to history section
        this.elements.benchmarkHistory?.scrollIntoView({ behavior: 'smooth' });
    }

    clearHistory() {
        if (confirm('Are you sure you want to clear all benchmark history? This action cannot be undone.')) {
            try {
                localStorage.removeItem('typing-benchmark-history');
                this.updateHistoryDisplay();
                this.updateCharts();
                console.log('Benchmark history cleared');
            } catch (error) {
                console.error('Error clearing benchmark history:', error);
            }
        }
    }

    exportHistory() {
        try {
            const history = this.getBenchmarkHistory();

            if (history.length === 0) {
                alert('No benchmark history to export.');
                return;
            }

            // Create CSV content
            const csvHeader = 'Date,WPM,Accuracy,Skill Level,Text Title,Total Characters,Correct Characters,Time Elapsed\n';
            const csvRows = history.map(result => {
                const date = new Date(result.date).toLocaleString();
                return `"${date}",${result.wpm},${result.accuracy},"${result.skillLevel}","${result.textTitle}",${result.totalCharacters},${result.correctCharacters},${result.timeElapsed}`;
            }).join('\n');

            const csvContent = csvHeader + csvRows;

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `typing-benchmark-history-${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('Benchmark history exported');

        } catch (error) {
            console.error('Error exporting benchmark history:', error);
            alert('Error exporting history. Please try again.');
        }
    }
}

    // Store app instances globally for theme changes
    let typingTestApp = null;
    let typingBenchmarkApp = null;

    // Initialize the applications when DOM is loaded
    document.addEventListener('DOMContentLoaded', async () => {
        typingTestApp = new TypingTest();
        typingBenchmarkApp = new TypingBenchmark();

        // Wait a bit for both apps to fully initialize, then restore the active section
        setTimeout(() => {
            initializeActiveSection();
        }, 100);
    });

    // Centralized function to handle tab restoration
    function initializeActiveSection() {
        // Restore the previously active section from localStorage
        const savedSection = localStorage.getItem('typing-test-active-section');
        const activeSection = savedSection === 'benchmark' ? 'benchmark' : 'practice';

        // Use the benchmark app's switchToSection method since it handles both tabs
        if (typingBenchmarkApp) {
            typingBenchmarkApp.switchToSection(activeSection);
        }
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
        const currentTheme = localStorage.getItem('typing-test-theme');
        if (currentTheme === 'system' && typingTestApp) {
            typingTestApp.setTheme('system');
        }
    });

    // Expose classes to global scope for debugging (in development only)
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        window.TypingTest = TypingTest;
        window.TypingBenchmark = TypingBenchmark;
    }

})(); // End of IIFE
