<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Easy Typing - Test and improve your typing speed</title>

    <!-- Immediate theme application to prevent FOUC -->
    <script>
      (function() {
        try {
          const theme = localStorage.getItem('typing-test-theme') || 'system';
          const root = document.documentElement;

          if (theme === 'dark') {
            root.classList.add('dark');
          } else if (theme === 'light') {
            root.classList.add('light');
          } else if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            root.classList.add(systemTheme);
          }
        } catch (e) {
          console.error('Could not apply theme from localStorage', e);
        }
      })();
    </script>

    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Fallback for disabled JavaScript -->
    <noscript>
        <style>
            .app::before {
                content: "This website requires JavaScript to function properly. Please enable JavaScript in your browser settings.";
                display: block;
                background: #fef2f2;
                color: #dc2626;
                padding: 1rem;
                border-radius: 0.5rem;
                margin-bottom: 2rem;
                text-align: center;
                font-weight: 600;
            }
        </style>
    </noscript>
</head>
<body>
    <div class="app">
        <!-- Header with Theme Toggle -->
        <div class="header">
            <div class="header-content">
                <div class="title-section">
                    <h1 class="main-title">Easy Typing</h1>
                    <p class="subtitle">Test and improve your typing speed with precision</p>
                </div>
                <div class="theme-toggle-container">
                    <div class="theme-toggle">
                        <button type="button" class="theme-btn" data-theme="light" title="Light theme">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="5"/>
                                <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                            </svg>
                        </button>
                        <button type="button" class="theme-btn" data-theme="dark" title="Dark theme">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                            </svg>
                        </button>
                        <button type="button" class="theme-btn" data-theme="system" title="System theme">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                <line x1="8" y1="21" x2="16" y2="21"/>
                                <line x1="12" y1="17" x2="12" y2="21"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="navigation-tabs">
            <button type="button" id="practice-tab" class="nav-tab active" data-section="practice">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Practice Mode
            </button>
            <button type="button" id="benchmark-tab" class="nav-tab" data-section="benchmark">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 11H7l5-8 5 8h-2l-1 7-4-7z"/>
                </svg>
                Typing Benchmark
            </button>
        </div>

        <!-- Practice Section -->
        <div id="practice-section" class="test-section active">
            <!-- Settings Panel -->
        <div class="settings-panel">
            <div class="settings-grid">
                <!-- Duration Selection -->
                <div class="setting-group">
                    <label for="duration-select" class="setting-label">Test Duration</label>
                    <select id="duration-select" class="setting-select">
                        <option value="30">30 seconds</option>
                        <option value="60" selected>60 seconds</option>
                        <option value="120">2 minutes</option>
                        <option value="300">5 minutes</option>
                    </select>
                </div>

                <!-- Difficulty Selection -->
                <div class="setting-group">
                    <label for="difficulty-select" class="setting-label">Difficulty Level</label>
                    <select id="difficulty-select" class="setting-select">
                        <option value="beginner">Beginner</option>
                        <option value="intermediate" selected>Intermediate</option>
                        <option value="advanced">Advanced</option>
                    </select>
                </div>

                <!-- Passage Selection -->
                <div class="setting-group">
                    <label for="passage-select" class="setting-label">Text Passage</label>
                    <select id="passage-select" class="setting-select">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
            </div>
        </div>

        <!-- Stats Display -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <polyline points="12,6 12,12 16,14"/>
                    </svg>
                </div>
                <div class="stat-value" id="time-left">60</div>
                <div class="stat-label">Time Left</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
                    </svg>
                </div>
                <div class="stat-value" id="wpm">0</div>
                <div class="stat-label">WPM</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 11l3 3l8-8"/>
                        <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9s4.03-9 9-9c1.51 0 2.93.37 4.18 1.03"/>
                    </svg>
                </div>
                <div class="stat-value" id="accuracy">100</div>
                <div class="stat-label">Accuracy %</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 9l6 6l6-6"/>
                    </svg>
                </div>
                <div class="stat-value" id="characters">0</div>
                <div class="stat-label">Characters</div>
            </div>
        </div>

        <!-- Text Display -->
        <div class="text-section">
            <h3 id="passage-title" class="passage-title">Loading...</h3>
            <div id="text-display" class="text-display">
                <!-- Text will be populated by JavaScript -->
            </div>
        </div>

        <!-- Input Area -->
        <div class="input-section">
            <textarea 
                id="typing-input" 
                class="typing-input" 
                placeholder="Click here and start typing to begin the test"
                rows="4"
            ></textarea>
        </div>

        <!-- Reset Button -->
        <div class="controls">
            <button type="button" id="reset-btn" class="reset-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="1,4 1,10 7,10"/>
                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
                </svg>
                Reset Test
            </button>
            <button type="button" id="exit-practice-btn" class="exit-practice-btn hidden">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 12l2 2 4-4"/>
                    <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9s4.03-9 9-9c1.66 0 3.22.45 4.56 1.23"/>
                </svg>
                Exit Practice
            </button>
        </div>

        <!-- Tricky Keys Section -->
        <div id="tricky-keys-section" class="tricky-keys-section">
            <div class="section-header">
                <h3 class="section-title">Your Tricky Keys</h3>
                <p class="section-description">Characters you mistype most frequently</p>
                <div class="section-actions">
                    <button type="button" id="clear-tricky-keys-btn" class="clear-btn" title="Clear all tricky keys data">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"/>
                            <path d="M19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1 2,2h4a2,2 0 0,1 2,2v2"/>
                            <line x1="10" y1="11" x2="10" y2="17"/>
                            <line x1="14" y1="11" x2="14" y2="17"/>
                        </svg>
                        Clear Data
                    </button>
                </div>
            </div>

            <div id="tricky-keys-list" class="tricky-keys-list">
                <div class="no-data-message">
                    <div class="no-data-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                            <path d="M12 17h.01"/>
                        </svg>
                    </div>
                    <h4>No tricky keys yet</h4>
                    <p>Start typing to identify your most challenging keys. The system will track your errors and help you practice specific characters you struggle with.</p>
                </div>
            </div>
        </div>

        <!-- Results Modal -->
        <div id="results-modal" class="modal-overlay hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">Test Results</h2>
                    <button type="button" id="close-modal" class="close-btn" title="Close results">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="results-grid">
                        <div class="result-item">
                            <div class="result-label">Words Per Minute</div>
                            <div class="result-value" id="final-wpm">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Accuracy</div>
                            <div class="result-value" id="final-accuracy">100%</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Total Characters</div>
                            <div class="result-value" id="final-characters">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Correct Characters</div>
                            <div class="result-value" id="final-correct">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Incorrect Characters</div>
                            <div class="result-value" id="final-incorrect">0</div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">Time Elapsed</div>
                            <div class="result-value" id="final-time">0s</div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" id="try-again-btn" class="primary-btn">Try Again</button>
                    </div>
                </div>
            </div>
        </div>
        </div> <!-- End Practice Section -->

        <!-- Benchmark Section -->
        <div id="benchmark-section" class="test-section">
            <div class="benchmark-intro">
                <h2 class="section-title">Typing Benchmark</h2>
                <p class="section-description">
                    Test your typing skills with standardized texts and track your progress over time.
                    Each benchmark test uses the same texts and duration for fair comparison.
                </p>
            </div>

            <!-- Benchmark Test Area -->
            <div id="benchmark-test-area" class="benchmark-test-area">
                <div class="benchmark-info">
                    <div class="info-card">
                        <h3>Test Details</h3>
                        <ul>
                            <li><strong>Duration:</strong> 90 seconds (fixed)</li>
                            <li><strong>Text:</strong> Standardized passages</li>
                            <li><strong>Goal:</strong> Measure your true skill level</li>
                        </ul>
                    </div>
                </div>

                <button type="button" id="start-benchmark-btn" class="start-benchmark-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="5,3 19,12 5,21"/>
                    </svg>
                    Start Benchmark Test
                </button>

                <!-- Benchmark Test Interface (hidden initially) -->
                <div id="benchmark-test-interface" class="benchmark-test-interface hidden">
                    <!-- Benchmark Stats Display -->
                    <div class="benchmark-stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                            </div>
                            <div class="stat-value" id="benchmark-time-left">90</div>
                            <div class="stat-label">Time Left</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
                                </svg>
                            </div>
                            <div class="stat-value" id="benchmark-wpm">0</div>
                            <div class="stat-label">WPM</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 12l2 2 4-4"/>
                                    <circle cx="12" cy="12" r="10"/>
                                </svg>
                            </div>
                            <div class="stat-value" id="benchmark-accuracy">100%</div>
                            <div class="stat-label">Accuracy</div>
                        </div>
                    </div>

                    <!-- Benchmark Text Display -->
                    <div class="benchmark-text-section">
                        <h3 id="benchmark-passage-title" class="passage-title">Benchmark Text</h3>
                        <div id="benchmark-text-display" class="text-display">
                            <!-- Text will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Benchmark Input Area -->
                    <div class="benchmark-input-section">
                        <textarea
                            id="benchmark-typing-input"
                            class="typing-input"
                            placeholder="Click here and start typing to begin the benchmark test"
                            rows="4"
                        ></textarea>
                    </div>
                </div>
            </div>

            <!-- Benchmark Results Display -->
            <div id="benchmark-results" class="benchmark-results hidden">
                <div class="results-header">
                    <h3>Benchmark Complete!</h3>
                </div>

                <div class="benchmark-score-display">
                    <div class="score-card primary">
                        <div class="score-label">Your Score</div>
                        <div class="score-value" id="benchmark-final-wpm">0</div>
                        <div class="score-unit">WPM</div>
                    </div>

                    <div class="score-card secondary">
                        <div class="score-label">Accuracy</div>
                        <div class="score-value" id="benchmark-final-accuracy">100%</div>
                    </div>
                </div>

                <div class="skill-level-display">
                    <div class="skill-level-badge" id="skill-level-badge">
                        <div class="badge-icon">🏆</div>
                        <div class="badge-text">
                            <div class="level-title" id="skill-level-title">Calculating...</div>
                            <div class="level-description" id="skill-level-description">Please wait...</div>
                        </div>
                    </div>
                </div>

                <div class="benchmark-actions">
                    <button type="button" id="benchmark-try-again-btn" class="primary-btn">Take Another Benchmark</button>
                    <button type="button" id="view-history-btn" class="secondary-btn">View History</button>
                </div>
            </div>

            <!-- Performance Charts -->
            <div id="benchmark-charts" class="benchmark-charts">
                <h3 class="charts-title">Your Progress</h3>
                <div class="charts-grid">
                    <div class="chart-container">
                        <h4>WPM Over Time</h4>
                        <canvas id="wpm-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h4>Accuracy Over Time</h4>
                        <canvas id="accuracy-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Benchmark History -->
            <div id="benchmark-history" class="benchmark-history">
                <div class="history-header">
                    <h3>Benchmark History</h3>
                    <div class="history-actions">
                        <button type="button" id="clear-history-btn" class="action-btn" title="Clear all benchmark history">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"/>
                            </svg>
                            Clear History
                        </button>
                        <button type="button" id="export-history-btn" class="action-btn" title="Export benchmark history">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                            Export
                        </button>
                    </div>
                </div>

                <div id="benchmark-history-table" class="history-table-container">
                    <div class="no-data-message">
                        <div class="no-data-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                <path d="M9 11H7l5-8 5 8h-2l-1 7-4-7z"/>
                            </svg>
                        </div>
                        <h4>No benchmark history yet</h4>
                        <p>Take your first benchmark test to start tracking your progress and see your improvement over time.</p>
                    </div>
                </div>
            </div>
        </div> <!-- End Benchmark Section -->
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="app.js"></script>
</body>
</html>
