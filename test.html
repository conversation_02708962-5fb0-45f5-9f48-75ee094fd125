<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Test - Feature Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Typing Test - Feature Verification</h1>
    
    <div class="test-section">
        <h2>Application Preview</h2>
        <p>The typing test application should load below. Test the following features:</p>
        <iframe src="index.html" title="Typing Test Application"></iframe>
    </div>
    
    <div class="test-section">
        <h2>Manual Test Checklist</h2>
        <div id="test-results">
            <div class="info">
                <strong>Instructions:</strong> Use the application above and check off each feature as you test it.
            </div>
            
            <h3>Core Functionality</h3>
            <div class="test-result">
                <input type="checkbox" id="test-load"> 
                <label for="test-load">✓ Application loads without errors</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-passages"> 
                <label for="test-passages">✓ Text passages load correctly</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-typing"> 
                <label for="test-typing">✓ Typing input works and highlights characters</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-timer"> 
                <label for="test-timer">✓ Timer starts when typing begins</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-stats"> 
                <label for="test-stats">✓ WPM and accuracy update in real-time</label>
            </div>
            
            <h3>Settings</h3>
            <div class="test-result">
                <input type="checkbox" id="test-duration"> 
                <label for="test-duration">✓ Duration setting changes work</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-difficulty"> 
                <label for="test-difficulty">✓ Difficulty level changes work</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-passage-select"> 
                <label for="test-passage-select">✓ Passage selection works</label>
            </div>
            
            <h3>Theme System</h3>
            <div class="test-result">
                <input type="checkbox" id="test-light-theme"> 
                <label for="test-light-theme">✓ Light theme works</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-dark-theme"> 
                <label for="test-dark-theme">✓ Dark theme works</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-system-theme"> 
                <label for="test-system-theme">✓ System theme works</label>
            </div>
            
            <h3>Test Completion</h3>
            <div class="test-result">
                <input type="checkbox" id="test-completion"> 
                <label for="test-completion">✓ Test completes when time runs out</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-results-modal"> 
                <label for="test-results-modal">✓ Results modal shows correct statistics</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-reset"> 
                <label for="test-reset">✓ Reset button works correctly</label>
            </div>
            
            <h3>Responsive Design</h3>
            <div class="test-result">
                <input type="checkbox" id="test-mobile"> 
                <label for="test-mobile">✓ Works well on mobile/small screens</label>
            </div>
            <div class="test-result">
                <input type="checkbox" id="test-desktop"> 
                <label for="test-desktop">✓ Works well on desktop/large screens</label>
            </div>
        </div>
        
        <button onclick="generateReport()">Generate Test Report</button>
        <button onclick="resetTests()">Reset All Tests</button>
    </div>
    
    <div class="test-section">
        <h2>Test Report</h2>
        <div id="report-output">
            <div class="info">Click "Generate Test Report" after completing the manual tests above.</div>
        </div>
    </div>
    
    <script>
        function generateReport() {
            const checkboxes = document.querySelectorAll('#test-results input[type="checkbox"]');
            const total = checkboxes.length;
            const passed = Array.from(checkboxes).filter(cb => cb.checked).length;
            const percentage = Math.round((passed / total) * 100);
            
            const reportOutput = document.getElementById('report-output');
            
            let reportClass = 'info';
            if (percentage === 100) {
                reportClass = 'pass';
            } else if (percentage < 70) {
                reportClass = 'fail';
            }
            
            reportOutput.innerHTML = `
                <div class="${reportClass}">
                    <h3>Test Results Summary</h3>
                    <p><strong>Tests Passed:</strong> ${passed} / ${total} (${percentage}%)</p>
                    <p><strong>Status:</strong> ${percentage === 100 ? 'All tests passed! ✅' : percentage >= 70 ? 'Most tests passed ⚠️' : 'Multiple tests failed ❌'}</p>
                    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
                </div>
            `;
        }
        
        function resetTests() {
            const checkboxes = document.querySelectorAll('#test-results input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            document.getElementById('report-output').innerHTML = '<div class="info">Click "Generate Test Report" after completing the manual tests above.</div>';
        }
        
        // Auto-save test progress
        document.addEventListener('change', function(e) {
            if (e.target.type === 'checkbox') {
                localStorage.setItem('typing-test-' + e.target.id, e.target.checked);
            }
        });
        
        // Load saved test progress
        window.addEventListener('load', function() {
            const checkboxes = document.querySelectorAll('#test-results input[type="checkbox"]');
            checkboxes.forEach(cb => {
                const saved = localStorage.getItem('typing-test-' + cb.id);
                if (saved === 'true') {
                    cb.checked = true;
                }
            });
        });
    </script>
</body>
</html>
