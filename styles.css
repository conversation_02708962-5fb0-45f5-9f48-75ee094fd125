/* CSS Variables for Theme Support */
:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --primary: #22c55e;
  --primary-foreground: #ffffff;
  --secondary: #3b82f6;
  --secondary-foreground: #ffffff;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f59e0b;
  --accent-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #22c55e;
  
  /* Typing test specific colors */
  --correct-char: #22c55e;
  --correct-char-bg: #f0fdf4;
  --incorrect-char: #ef4444;
  --incorrect-char-bg: #fef2f2;
  --current-char: #eab308;
  --current-char-bg: #fefce8;
  --untyped-char: #64748b;
  --cursor: #22c55e;

  /* Additional colors for compatibility */
  --text-primary: #0f172a;
  --text-secondary: #64748b;
  --card-bg: #ffffff;
  --hover-bg: #f8fafc;
  --primary-color: #22c55e;
  --primary-hover: #16a34a;
  --accent-color: #3b82f6;
  --border-color: #e2e8f0;
  
  /* Spacing and sizing */
  --radius: 0.5rem;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.dark {
  /* Dark theme colors */
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #0f172a;
  --card-foreground: #f8fafc;
  --primary: #22c55e;
  --primary-foreground: #ffffff;
  --secondary: #3b82f6;
  --secondary-foreground: #ffffff;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #f59e0b;
  --accent-foreground: #0f172a;
  --border: #1e293b;
  --input: #1e293b;
  --ring: #22c55e;
  
  /* Typing test specific colors - dark theme */
  --correct-char: #22c55e;
  --correct-char-bg: #052e16;
  --incorrect-char: #ef4444;
  --incorrect-char-bg: #450a0a;
  --current-char: #eab308;
  --current-char-bg: #422006;
  --untyped-char: #94a3b8;
  --cursor: #22c55e;

  /* Additional colors for compatibility - dark theme */
  --text-primary: #f8fafc;
  --text-secondary: #94a3b8;
  --card-bg: #0f172a;
  --hover-bg: #1e293b;
  --primary-color: #22c55e;
  --primary-hover: #16a34a;
  --accent-color: #3b82f6;
  --border-color: #1e293b;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'JetBrains Mono', monospace;
  background-color: var(--background);
  color: var(--foreground);
  line-height: 1.6;
  transition: background-color 0.15s ease, color 0.15s ease;
}

.app {
  min-height: 100vh;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Styles */
.header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.title-section {
  text-align: center;
  flex: 1;
}

.main-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--muted-foreground);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  background-color: var(--muted);
  border-radius: var(--radius);
  padding: 0.25rem;
  gap: 0.25rem;
  transition: background-color 0.15s ease;
}

.theme-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: calc(var(--radius) - 0.125rem);
  background: transparent;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.15s ease;
}

.theme-btn:hover {
  background-color: var(--background);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

.theme-btn.active {
  background-color: var(--background);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

/* Settings Panel */
.settings-panel {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  transition: background-color 0.15s ease, border-color 0.15s ease;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-label {
  font-weight: 600;
  color: var(--card-foreground);
  font-size: 0.875rem;
}

.setting-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--input);
  border-radius: var(--radius);
  background-color: var(--background);
  color: var(--foreground);
  font-size: 1rem;
  transition: all 0.15s ease;
}

.setting-select:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: all 0.15s ease;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  color: var(--primary);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--card-foreground);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

/* Text Section */
.text-section {
  margin-bottom: 2rem;
}

.passage-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 1rem;
}

.text-display {
  background-color: var(--card);
  border: 2px solid var(--border);
  border-radius: var(--radius);
  padding: 2rem;
  font-size: 1.25rem;
  line-height: 1.8;
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-word;
  white-space: normal;
  box-shadow: var(--shadow-lg);
  transition: all 0.15s ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; /* Prevent text selection for anti-cheating */
}

/* Typing Character Styles */
.typing-char {
  position: relative;
  display: inline;
  word-break: normal;
}

.typing-char.correct {
  color: var(--correct-char);
  background-color: var(--correct-char-bg);
}

.typing-char.incorrect {
  color: var(--incorrect-char);
  background-color: var(--incorrect-char-bg);
}

.typing-char.current {
  color: var(--current-char);
  background-color: var(--current-char-bg);
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.typing-char.untyped {
  color: var(--untyped-char);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Input Section */
.input-section {
  margin-bottom: 2rem;
}

.typing-input {
  width: 100%;
  padding: 1.5rem;
  border: 2px solid var(--input);
  border-radius: var(--radius);
  background-color: var(--background);
  color: var(--foreground);
  font-size: 1.125rem;
  font-family: inherit;
  resize: none;
  transition: all 0.15s ease;
}

.typing-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.typing-input:disabled {
  background-color: var(--muted);
  color: var(--muted-foreground);
  cursor: not-allowed;
}

/* Controls */
.controls {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.reset-btn:hover {
  background-color: var(--primary);
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-overlay.hidden {
  display: none;
}

.modal-content {
  background-color: var(--card);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--card-foreground);
}

.close-btn {
  background: none;
  border: none;
  color: var(--muted-foreground);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius);
  transition: all 0.15s ease;
}

.close-btn:hover {
  color: var(--foreground);
  background-color: var(--muted);
}

.modal-body {
  padding: 1.5rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.result-item {
  text-align: center;
  padding: 1rem;
  background-color: var(--muted);
  border-radius: var(--radius);
}

.result-label {
  font-size: 0.875rem;
  color: var(--muted-foreground);
  margin-bottom: 0.5rem;
}

.result-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--card-foreground);
}

.modal-actions {
  display: flex;
  justify-content: center;
}

.primary-btn {
  padding: 0.75rem 2rem;
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.primary-btn:hover {
  background-color: var(--primary);
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem 0.5rem;
  }
  
  .main-title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .text-display {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .typing-input {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  /* Improve touch targets */
  button, select, textarea, input {
    min-height: 44px;
  }
  
  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .text-display {
    font-size: 0.875rem;
    min-height: 150px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
  opacity: 0.3;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 0.5;
}

/* Selection styles */
::selection {
  background-color: rgba(34, 197, 94, 0.2);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Tricky Keys Section */
.tricky-keys-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
  margin: 0;
}

.section-description {
  font-size: 0.875rem;
  color: var(--muted-foreground);
  margin: 0.25rem 0 0 0;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: color-mix(in srgb, var(--primary) 90%, black);
}

.clear-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  color: var(--muted-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: var(--muted);
  color: var(--foreground);
}

.exit-practice-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exit-practice-btn:hover {
  background: color-mix(in srgb, var(--secondary) 90%, black);
}

/* Tricky Keys List */
.tricky-keys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.tricky-key-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--muted);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.tricky-key-item:hover {
  background: color-mix(in srgb, var(--muted) 80%, var(--foreground));
  border-color: color-mix(in srgb, var(--border) 70%, var(--foreground));
}

.key-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.key-character {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border-radius: var(--radius);
  font-size: 1.125rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.key-stats {
  flex: 1;
}

.error-count {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.common-mistake {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.mistake-char {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--accent);
  background: color-mix(in srgb, var(--accent) 10%, transparent);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.practice-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.practice-btn:hover {
  background: color-mix(in srgb, var(--primary) 90%, black);
  transform: translateY(-1px);
}

.practice-btn:active {
  transform: translateY(0);
}

/* No Data Message */
.no-data-message {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--muted-foreground);
}

.no-data-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data-message h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 0.5rem 0;
}

.no-data-message p {
  font-size: 0.875rem;
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tricky-keys-section {
    margin-top: 1.5rem;
    padding: 1rem;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .section-actions {
    justify-content: flex-end;
  }

  .tricky-keys-grid {
    grid-template-columns: 1fr;
  }

  .tricky-key-item {
    padding: 0.75rem;
  }

  .key-info {
    gap: 0.75rem;
  }

  .key-character {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }

  .no-data-message {
    padding: 2rem 1rem;
  }
}

/* Navigation Tabs */
.navigation-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid var(--border);
  padding-bottom: 0;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--muted-foreground);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 0.5rem 0.5rem 0 0;
  transition: all 0.2s ease;
  position: relative;
  bottom: -2px;
}

.nav-tab:hover {
  background: var(--card);
  color: var(--foreground);
}

.nav-tab.active {
  background: var(--card);
  color: var(--primary);
  border-bottom: 2px solid var(--primary);
}

.nav-tab svg {
  width: 20px;
  height: 20px;
}

/* Test Sections */
.test-section {
  display: none;
}

.test-section.active {
  display: block;
}

/* Benchmark Section */
.benchmark-intro {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--foreground);
  margin-bottom: 0.5rem;
}

.section-description {
  font-size: 1.1rem;
  color: var(--muted-foreground);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Benchmark Test Area */
.benchmark-test-area {
  margin-bottom: 3rem;
}

.benchmark-info {
  margin-bottom: 2rem;
}

.info-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  max-width: 500px;
  margin: 0 auto;
}

.info-card h3 {
  color: var(--foreground);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-card li {
  padding: 0.5rem 0;
  color: var(--muted-foreground);
  border-bottom: 1px solid var(--border);
}

.info-card li:last-child {
  border-bottom: none;
}

.info-card strong {
  color: var(--foreground);
}

/* Start Benchmark Button */
.start-benchmark-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.start-benchmark-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
}

.start-benchmark-btn svg {
  width: 24px;
  height: 24px;
}

/* Benchmark Test Interface */
.benchmark-test-interface {
  margin-top: 2rem;
}

.benchmark-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.benchmark-text-section {
  margin-bottom: 2rem;
}

.benchmark-input-section {
  margin-bottom: 2rem;
}

/* Benchmark Results */
.benchmark-results {
  text-align: center;
  margin-bottom: 3rem;
}

.results-header h3 {
  font-size: 2rem;
  color: var(--foreground);
  margin-bottom: 2rem;
}

.benchmark-score-display {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.score-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  padding: 2rem;
  min-width: 200px;
  text-align: center;
}

.score-card.primary {
  border-color: var(--primary);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(59, 130, 246, 0.1));
}

.score-card.secondary {
  border-color: var(--accent);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(16, 185, 129, 0.1));
}

.score-label {
  font-size: 0.9rem;
  color: var(--muted-foreground);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.score-value {
  font-size: 3rem;
  font-weight: 700;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.score-unit {
  font-size: 1rem;
  color: var(--muted-foreground);
  font-weight: 500;
}

/* Skill Level Display */
.skill-level-display {
  margin-bottom: 2rem;
}

.skill-level-badge {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--card);
  border: 2px solid var(--primary);
  border-radius: 1rem;
  padding: 1.5rem;
  max-width: 400px;
  margin: 0 auto;
}

.badge-icon {
  font-size: 2rem;
}

.badge-text {
  text-align: left;
}

.level-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.level-description {
  font-size: 1rem;
  color: var(--muted-foreground);
  line-height: 1.4;
}

/* Benchmark Actions */
.benchmark-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.secondary-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--card);
  color: var(--foreground);
  border: 1px solid var(--border);
}

.secondary-btn:hover {
  background: var(--muted);
  border-color: var(--primary);
}

/* Charts */
.benchmark-charts {
  margin-bottom: 3rem;
}

.charts-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--foreground);
  text-align: center;
  margin-bottom: 2rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.chart-container {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.chart-container h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 1rem;
  text-align: center;
}

.chart-container canvas {
  width: 100% !important;
  height: 300px !important;
}

/* History */
.benchmark-history {
  margin-bottom: 2rem;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.history-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--foreground);
  margin: 0;
}

.history-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  color: var(--muted-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--muted);
  color: var(--foreground);
  border-color: var(--primary);
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

/* History Table */
.history-table-container {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.history-table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th,
.history-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.history-table th {
  background: var(--muted);
  color: var(--foreground);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-table td {
  color: var(--muted-foreground);
}

.history-table tr:last-child td {
  border-bottom: none;
}

.history-table tr:hover {
  background: var(--muted);
}

.wpm-cell {
  font-weight: 600;
  color: var(--primary);
}

.accuracy-cell {
  font-weight: 600;
  color: var(--secondary);
}

.skill-level-cell {
  text-align: center;
}

.skill-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.skill-novice {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.skill-intermediate {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.skill-proficient {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.skill-advanced {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.skill-expert {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.text-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

/* Responsive Design for Benchmark */
@media (max-width: 768px) {
  .navigation-tabs {
    flex-direction: column;
    gap: 0;
    border-bottom: none;
  }

  .nav-tab {
    border-radius: 0.5rem;
    border-bottom: 1px solid var(--border);
    bottom: 0;
  }

  .nav-tab.active {
    border-bottom: 1px solid var(--primary);
  }

  .benchmark-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .benchmark-score-display {
    flex-direction: column;
    align-items: center;
  }

  .score-card {
    min-width: 250px;
  }

  .skill-level-badge {
    flex-direction: column;
    text-align: center;
  }

  .badge-text {
    text-align: center;
  }

  .benchmark-actions {
    flex-direction: column;
    align-items: center;
  }

  .primary-btn, .secondary-btn {
    width: 100%;
    max-width: 250px;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    padding: 1rem;
  }

  .chart-container canvas {
    height: 250px !important;
  }

  .history-header {
    flex-direction: column;
    align-items: stretch;
  }

  .history-actions {
    justify-content: center;
  }

  .history-table-container {
    overflow-x: auto;
  }

  .history-table {
    min-width: 600px;
  }

  .history-table th,
  .history-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .text-cell {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .benchmark-stats-grid {
    grid-template-columns: 1fr;
  }

  .score-card {
    min-width: 200px;
  }

  .score-value {
    font-size: 2.5rem;
  }

  .level-title {
    font-size: 1.25rem;
  }

  .charts-grid {
    gap: 1rem;
  }

  .chart-container canvas {
    height: 200px !important;
  }

  .history-table {
    min-width: 500px;
  }

  .history-table th,
  .history-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }

  .text-cell {
    max-width: 100px;
  }
}
